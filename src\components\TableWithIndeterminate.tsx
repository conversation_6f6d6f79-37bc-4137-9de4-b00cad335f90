import React, { useRef, useState } from "react";
import { ListTable } from "@visactor/react-vtable";

function TableWithIndeterminate() {
  const tableRef = useRef(null);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checked, setChecked] = useState(false);

  const records = [
    { id: 1, name: "<PERSON>", age: 28, gender: "Male" },
    { id: 2, name: "<PERSON>", age: 32, gender: "Female" },
    { id: 3, name: "<PERSON>", age: 45, gender: "Male" },
  ];

  const columns = [
    {
      field: "checkbox",
      title: "选择",
      width: 80,
      headerType: "checkbox",
      cellType: "checkbox",
      headerStyle: {
        indeterminate: indeterminate, // 控制表头复选框的 indeterminate 状态
      },
      style: { textAlign: "center" },
    },
    { field: "name", title: "姓名", width: 150 },
    { field: "age", title: "年龄", width: 100 },
    { field: "gender", title: "性别", width: 100 },
  ];

  const option = {
    records,
    columns,
    checkbox: {
      checked: checked,
      onChange: (state, col, row, table) => {
        updateHeaderCheckboxState(table);
      },
    },
  };

  // 更新表头复选框状态
  const updateHeaderCheckboxState = (table) => {
    const selectedCount = table.getCheckedRecords().length;
    const totalCount = table.recordsCount;

    if (selectedCount === 0) {
      setChecked(false);
      setIndeterminate(false);
    } else if (selectedCount === totalCount) {
      setChecked(true);
      setIndeterminate(false);
    } else {
      setChecked(false);
      setIndeterminate(true);
    }
  };

  return (
    <div style={{ width: "100%", height: "500px" }}>
      <ListTable ref={tableRef} option={option} />
    </div>
  );
}

export default TableWithIndeterminate;
